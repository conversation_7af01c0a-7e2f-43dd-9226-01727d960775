using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;

namespace ZeroDateStrat.Services;

public interface IPredictiveCachingService
{
    Task PredictAndCacheAsync();
    Task PredictMarketDataAsync();
    Task PredictTradingOpportunitiesAsync();
    void RegisterAccessPattern(string key, DateTime accessTime);
}

public class AccessPattern
{
    public string Key { get; set; } = string.Empty;
    public List<DateTime> AccessTimes { get; set; } = new();
    public TimeSpan PredictedNextAccess { get; set; }
    public double AccessFrequency { get; set; }
    public int Priority { get; set; }
}

public class PredictiveCachingService : IPredictiveCachingService, IHostedService
{
    private readonly IHighPerformanceCacheService _cache;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IOptionsChainService _optionsChainService;
    private readonly ILogger<PredictiveCachingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IPerformanceMonitoringService _performanceMonitor;
    
    private readonly ConcurrentDictionary<string, AccessPattern> _accessPatterns;
    private readonly Timer _predictionTimer;
    private readonly Timer _patternAnalysisTimer;

    public PredictiveCachingService(
        IHighPerformanceCacheService cache,
        IMarketRegimeAnalyzer marketRegimeAnalyzer,
        IPolygonDataService polygonDataService,
        IOptionsChainService optionsChainService,
        ILogger<PredictiveCachingService> logger,
        IConfiguration configuration,
        IPerformanceMonitoringService performanceMonitor)
    {
        _cache = cache;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
        _polygonDataService = polygonDataService;
        _optionsChainService = optionsChainService;
        _logger = logger;
        _configuration = configuration;
        _performanceMonitor = performanceMonitor;
        
        _accessPatterns = new ConcurrentDictionary<string, AccessPattern>();
        
        // Timer for predictive caching (every 30 seconds)
        _predictionTimer = new Timer(async _ => await PredictAndCacheAsync(), 
            null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
        // Timer for pattern analysis (every 5 minutes)
        _patternAnalysisTimer = new Timer(async _ => await AnalyzeAccessPatternsAsync(), 
            null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔮 Starting predictive caching service...");
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _predictionTimer?.Dispose();
        _patternAnalysisTimer?.Dispose();
        _logger.LogInformation("Predictive caching service stopped");
        return Task.CompletedTask;
    }

    public void RegisterAccessPattern(string key, DateTime accessTime)
    {
        _accessPatterns.AddOrUpdate(key, 
            new AccessPattern 
            { 
                Key = key, 
                AccessTimes = new List<DateTime> { accessTime } 
            },
            (k, existing) =>
            {
                existing.AccessTimes.Add(accessTime);
                
                // Keep only last 100 access times for performance
                if (existing.AccessTimes.Count > 100)
                {
                    existing.AccessTimes = existing.AccessTimes.TakeLast(50).ToList();
                }
                
                return existing;
            });
    }

    public async Task PredictAndCacheAsync()
    {
        try
        {
            await _performanceMonitor.MonitorAsync("predictive_caching", async () =>
            {
                var tasks = new List<Task>
                {
                    PredictMarketDataAsync(),
                    PredictTradingOpportunitiesAsync(),
                    PredictTimeBasedDataAsync()
                };

                await Task.WhenAll(tasks);
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in predictive caching");
        }
    }

    public async Task PredictMarketDataAsync()
    {
        try
        {
            var now = DateTime.Now.TimeOfDay;
            var isMarketHours = now >= TimeSpan.FromHours(9.5) && now <= TimeSpan.FromHours(16);
            
            if (!isMarketHours) return;

            // Predict VIX data needs based on market volatility patterns
            var currentVix = await _cache.GetAsync<decimal>("vix_current");
            if (currentVix == null || currentVix > 30) // High volatility = more frequent VIX checks
            {
                _logger.LogDebug("🔮 Pre-caching VIX data due to high volatility");
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _marketRegimeAnalyzer.GetVixAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Failed to predictively cache VIX");
                    }
                });
            }

            // Predict market regime changes around key times
            if (IsKeyMarketTime(now))
            {
                _logger.LogDebug("🔮 Pre-caching market regime data for key time: {Time}", now);
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
                        await _marketRegimeAnalyzer.IsGoodTradingEnvironmentAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Failed to predictively cache market regime");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in predictive market data caching");
        }
    }

    public async Task PredictTradingOpportunitiesAsync()
    {
        try
        {
            var now = DateTime.Now.TimeOfDay;
            var entryWindow = now >= TimeSpan.FromHours(9.75) && now <= TimeSpan.FromHours(10.5); // 9:45-10:30
            
            if (entryWindow)
            {
                _logger.LogDebug("🔮 Pre-caching trading data for entry window");
                
                // Predict options chain needs for SPX during entry window
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var spxChain = await _cache.GetAsync<object>("options_chain_SPX");
                        if (spxChain == null)
                        {
                            await _optionsChainService.GetOptionsChainAsync("SPX");
                            _logger.LogDebug("🔮 Pre-cached SPX options chain");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Failed to predictively cache SPX options");
                    }
                });

                // Predict strategy recommendations
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _marketRegimeAnalyzer.GetRecommendedStrategiesAsync();
                        _logger.LogDebug("🔮 Pre-cached strategy recommendations");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Failed to predictively cache strategies");
                    }
                });
            }

            // Predict position management needs near market close
            var closeWindow = now >= TimeSpan.FromHours(15.5) && now <= TimeSpan.FromHours(16); // 3:30-4:00
            if (closeWindow)
            {
                _logger.LogDebug("🔮 Pre-caching position data for market close");
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Pre-cache current positions for close-of-day management
                        var positions = await _cache.GetAsync<object>("current_positions");
                        if (positions == null)
                        {
                            // This would trigger the actual service to cache positions
                            _logger.LogDebug("🔮 Triggering position data refresh for close");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Failed to predictively cache positions");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in predictive trading opportunities caching");
        }
    }

    private async Task PredictTimeBasedDataAsync()
    {
        try
        {
            var now = DateTime.Now;
            
            // Predict data needs based on learned access patterns
            var highPriorityPatterns = _accessPatterns.Values
                .Where(p => p.AccessFrequency > 0.1 && p.Priority > 5) // Frequently accessed, high priority
                .Where(p => ShouldPredictivelyCache(p, now))
                .ToList();

            foreach (var pattern in highPriorityPatterns.Take(5)) // Limit to top 5 to avoid overload
            {
                _logger.LogDebug("🔮 Predictively caching based on pattern: {Key}", pattern.Key);
                
                // This is a simplified prediction - in practice, you'd call the appropriate service
                // based on the key pattern (e.g., if key contains "vix", call VIX service)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        if (pattern.Key.Contains("vix", StringComparison.OrdinalIgnoreCase))
                        {
                            await _marketRegimeAnalyzer.GetVixAsync();
                        }
                        else if (pattern.Key.Contains("regime", StringComparison.OrdinalIgnoreCase))
                        {
                            await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
                        }
                        // Add more pattern-based predictions as needed
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Failed predictive caching for pattern {Key}", pattern.Key);
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in time-based predictive caching");
        }
    }

    private async Task AnalyzeAccessPatternsAsync()
    {
        try
        {
            await _performanceMonitor.MonitorAsync("pattern_analysis", async () =>
            {
                var now = DateTime.UtcNow;
                var patternsAnalyzed = 0;

                foreach (var kvp in _accessPatterns)
                {
                    var pattern = kvp.Value;
                    
                    if (pattern.AccessTimes.Count < 2) continue;

                    // Calculate access frequency (accesses per hour)
                    var timeSpan = pattern.AccessTimes.Max() - pattern.AccessTimes.Min();
                    if (timeSpan.TotalHours > 0)
                    {
                        pattern.AccessFrequency = pattern.AccessTimes.Count / timeSpan.TotalHours;
                    }

                    // Calculate average time between accesses
                    var intervals = new List<TimeSpan>();
                    for (int i = 1; i < pattern.AccessTimes.Count; i++)
                    {
                        intervals.Add(pattern.AccessTimes[i] - pattern.AccessTimes[i - 1]);
                    }

                    if (intervals.Any())
                    {
                        var avgInterval = TimeSpan.FromTicks((long)intervals.Average(i => i.Ticks));
                        pattern.PredictedNextAccess = avgInterval;
                    }

                    // Assign priority based on frequency and recency
                    var lastAccess = pattern.AccessTimes.Max();
                    var recencyScore = Math.Max(0, 10 - (now - lastAccess).TotalHours);
                    var frequencyScore = Math.Min(10, pattern.AccessFrequency);
                    pattern.Priority = (int)(recencyScore + frequencyScore);

                    patternsAnalyzed++;
                }

                if (patternsAnalyzed > 0)
                {
                    _logger.LogDebug("📊 Analyzed {Count} access patterns", patternsAnalyzed);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error analyzing access patterns");
        }
    }

    private bool ShouldPredictivelyCache(AccessPattern pattern, DateTime now)
    {
        if (pattern.AccessTimes.Count < 3) return false;

        var lastAccess = pattern.AccessTimes.Max();
        var timeSinceLastAccess = now - lastAccess;

        // If we predict the next access should happen soon, cache it
        return timeSinceLastAccess >= pattern.PredictedNextAccess.Multiply(0.8);
    }

    private bool IsKeyMarketTime(TimeSpan time)
    {
        // Key times when market regime might change
        var keyTimes = new[]
        {
            TimeSpan.FromHours(9.5),   // Market open
            TimeSpan.FromHours(10),    // Post-open volatility
            TimeSpan.FromHours(11),    // Mid-morning
            TimeSpan.FromHours(12),    // Lunch time
            TimeSpan.FromHours(14),    // Afternoon session
            TimeSpan.FromHours(15),    // Power hour
            TimeSpan.FromHours(15.5),  // Pre-close
            TimeSpan.FromHours(16)     // Market close
        };

        return keyTimes.Any(kt => Math.Abs((time - kt).TotalMinutes) < 5);
    }
}
