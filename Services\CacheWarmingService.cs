using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace ZeroDateStrat.Services;

public interface ICacheWarmingService
{
    Task WarmCacheAsync();
    Task WarmCriticalDataAsync();
    Task WarmMarketDataAsync();
    Task WarmTradingDataAsync();
}

public class CacheWarmingService : ICacheWarmingService, IHostedService
{
    private readonly IHighPerformanceCacheService _cache;
    private readonly IAlpacaService _alpacaService;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;
    private readonly IOptionsChainService _optionsChainService;
    private readonly ILogger<CacheWarmingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IPerformanceMonitoringService _performanceMonitor;

    public CacheWarmingService(
        IHighPerformanceCacheService cache,
        IAlpacaService alpacaService,
        IPolygonDataService polygonDataService,
        IMarketRegimeAnalyzer marketRegimeAnalyzer,
        IOptionsChainService optionsChainService,
        ILogger<CacheWarmingService> logger,
        IConfiguration configuration,
        IPerformanceMonitoringService performanceMonitor)
    {
        _cache = cache;
        _alpacaService = alpacaService;
        _polygonDataService = polygonDataService;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
        _optionsChainService = optionsChainService;
        _logger = logger;
        _configuration = configuration;
        _performanceMonitor = performanceMonitor;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting cache warming service...");
        
        // Start cache warming in background (don't block startup)
        _ = Task.Run(async () =>
        {
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(10), cancellationToken); // Wait for system to stabilize
                await WarmCacheAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache warming");
            }
        }, cancellationToken);

        return;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cache warming service stopped");
        return Task.CompletedTask;
    }

    public async Task WarmCacheAsync()
    {
        var startTime = DateTime.UtcNow;
        _logger.LogInformation("🔥 Starting comprehensive cache warming for i9-12900K system...");

        try
        {
            // Warm cache in parallel for maximum efficiency on 16-core system
            var warmingTasks = new List<Task>
            {
                WarmCriticalDataAsync(),
                WarmMarketDataAsync(),
                WarmTradingDataAsync()
            };

            await Task.WhenAll(warmingTasks);

            var duration = DateTime.UtcNow - startTime;
            var stats = _cache.GetStatistics();
            
            _logger.LogInformation("✅ Cache warming completed in {Duration:F1}s", duration.TotalSeconds);
            _logger.LogInformation("📊 Cache stats: {EntryCount} entries, {HitRatio:P1} hit ratio", 
                stats.EntryCount, stats.HitRatio);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Cache warming failed");
        }
    }

    public async Task WarmCriticalDataAsync()
    {
        await _performanceMonitor.MonitorAsync("cache_warm_critical", async () =>
        {
            _logger.LogInformation("🔥 Warming critical trading data...");

            var tasks = new List<Task>();

            // 1. Account Information (High Priority)
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    var account = await _alpacaService.GetAccountAsync();
                    if (account != null)
                    {
                        await _cache.SetAsync("account_info", account, TimeSpan.FromMinutes(30));
                        _logger.LogDebug("✅ Cached account info: ${Equity:F2} equity", account.Equity);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm account data");
                }
            }));

            // 2. Current Positions (High Priority)
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    var positions = await _alpacaService.GetPositionsAsync();
                    if (positions?.Any() == true)
                    {
                        await _cache.SetAsync("current_positions", positions, TimeSpan.FromMinutes(20));
                        _logger.LogDebug("✅ Cached {Count} positions", positions.Count());
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm positions data");
                }
            }));

            // 3. VIX Data (Critical for market regime)
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    var vix = await _marketRegimeAnalyzer.GetVixAsync();
                    _logger.LogDebug("✅ Cached VIX: {Vix:F2}", vix);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm VIX data");
                }
            }));

            await Task.WhenAll(tasks);
            _logger.LogInformation("✅ Critical data warming completed");
        });
    }

    public async Task WarmMarketDataAsync()
    {
        await _performanceMonitor.MonitorAsync("cache_warm_market", async () =>
        {
            _logger.LogInformation("🔥 Warming market data...");

            var symbols = new[] { "SPY", "QQQ", "IWM", "SPX", "VIX" };
            var tasks = new List<Task>();

            foreach (var symbol in symbols)
            {
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        // Get current quote
                        var quote = await _polygonDataService.GetQuoteAsync(symbol);
                        if (quote != null)
                        {
                            await _cache.SetAsync($"quote_{symbol}", quote, TimeSpan.FromSeconds(30));
                            _logger.LogDebug("✅ Cached quote for {Symbol}", symbol);
                        }

                        // Get market trend
                        var trend = await _marketRegimeAnalyzer.GetMarketTrendAsync(symbol);
                        await _cache.SetAsync($"trend_{symbol}", trend, TimeSpan.FromMinutes(5));
                        _logger.LogDebug("✅ Cached trend for {Symbol}: {Trend}", symbol, trend);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to warm market data for {Symbol}", symbol);
                    }
                }));
            }

            await Task.WhenAll(tasks);
            _logger.LogInformation("✅ Market data warming completed");
        });
    }

    public async Task WarmTradingDataAsync()
    {
        await _performanceMonitor.MonitorAsync("cache_warm_trading", async () =>
        {
            _logger.LogInformation("🔥 Warming trading-specific data...");

            var tasks = new List<Task>();

            // 1. Market Regime Analysis
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
                    await _cache.SetAsync("current_market_regime", regime, TimeSpan.FromMinutes(5));
                    _logger.LogDebug("✅ Cached market regime: {Regime}", regime.VolatilityRegime);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm market regime data");
                }
            }));

            // 2. Trading Environment Check
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    var isGoodEnvironment = await _marketRegimeAnalyzer.IsGoodTradingEnvironmentAsync();
                    await _cache.SetAsync("trading_environment_good", isGoodEnvironment, TimeSpan.FromMinutes(2));
                    _logger.LogDebug("✅ Cached trading environment: {IsGood}", isGoodEnvironment);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm trading environment data");
                }
            }));

            // 3. Recommended Strategies
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    var strategies = await _marketRegimeAnalyzer.GetRecommendedStrategiesAsync();
                    await _cache.SetAsync("recommended_strategies", strategies, TimeSpan.FromMinutes(10));
                    _logger.LogDebug("✅ Cached {Count} recommended strategies", strategies.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm strategies data");
                }
            }));

            // 4. SPX Options Chain (if market is open)
            if (IsMarketHours())
            {
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        var optionsChain = await _optionsChainService.GetOptionsChainAsync("SPX");
                        if (optionsChain != null)
                        {
                            await _cache.SetAsync("options_chain_SPX", optionsChain, TimeSpan.FromMinutes(10));
                            _logger.LogDebug("✅ Cached SPX options chain");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to warm SPX options chain");
                    }
                }));
            }

            await Task.WhenAll(tasks);
            _logger.LogInformation("✅ Trading data warming completed");
        });
    }

    private bool IsMarketHours()
    {
        var now = DateTime.Now.TimeOfDay;
        var marketOpen = TimeSpan.FromHours(9.5);  // 9:30 AM
        var marketClose = TimeSpan.FromHours(16);  // 4:00 PM
        
        return DateTime.Now.DayOfWeek != DayOfWeek.Saturday && 
               DateTime.Now.DayOfWeek != DayOfWeek.Sunday &&
               now >= marketOpen && now <= marketClose;
    }
}
