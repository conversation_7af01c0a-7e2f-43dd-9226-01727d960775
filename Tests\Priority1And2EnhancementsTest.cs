using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Strategies;
using ZeroDateStrat.Utils;
using ILogger = Serilog.ILogger;

namespace ZeroDateStrat.Tests;

public static class Priority1And2EnhancementsTest
{
    public static async Task RunPriority1And2EnhancementsTest()
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File($"logs/priority1-2-enhancements-test-{DateTime.Now:yyyyMMdd}.txt")
            .CreateLogger();

        var logger = Log.Logger;

        try
        {
            logger.Information("🚀 Starting Priority 1 & 2 Enhancements Test");
            logger.Information("Testing: Enhanced Entry Timing, Smart Exit Strategy, Portfolio Heat Management, Iron Condor, Broken Wing Butterfly");

            // Build host with all services
            var host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                })
                .ConfigureServices((context, services) =>
                {
                    // Register all required services
                    services.AddSingleton<IAlpacaService, AlpacaService>();
                    services.AddSingleton<IPolygonDataService, PolygonDataService>();
                    services.AddSingleton<IAlpacaVixService, AlpacaVixService>();
                    services.AddSingleton<IMarketRegimeAnalyzer>(provider => new MarketRegimeAnalyzer(
                        provider.GetRequiredService<ILogger<MarketRegimeAnalyzer>>(),
                        provider.GetRequiredService<IConfiguration>(),
                        provider.GetRequiredService<IAlpacaService>(),
                        provider.GetRequiredService<IPolygonDataService>(),
                        provider.GetRequiredService<IAlpacaVixService>()));
                    services.AddSingleton<ITradingCalendarService, TradingCalendarService>();
                    services.AddSingleton<IOptionsScanner, OptionsScanner>();
                    services.AddSingleton<IRiskManager, RiskManager>();
                    services.AddSingleton<IZeroDteStrategy, ZeroDteStrategy>();
                    services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
                    services.AddSingleton<ISecurityService, SecurityService>();
                    services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();
                    services.AddHttpClient<IPolygonDataService, PolygonDataService>();
                    services.AddLogging(builder => builder.AddSerilog());
                })
                .Build();

            // Get services
            var strategy = host.Services.GetRequiredService<IZeroDteStrategy>();
            var optionsScanner = host.Services.GetRequiredService<IOptionsScanner>();
            var alpacaService = host.Services.GetRequiredService<IAlpacaService>();
            var marketRegimeAnalyzer = host.Services.GetRequiredService<IMarketRegimeAnalyzer>();

            // Initialize Alpaca service
            logger.Information("📡 Initializing Alpaca service...");
            var alpacaInitialized = await alpacaService.InitializeAsync();
            if (!alpacaInitialized)
            {
                logger.Error("❌ Failed to initialize Alpaca service");
                return;
            }
            logger.Information("✅ Alpaca service initialized successfully");

            // Test 1: Enhanced Entry Timing
            logger.Information("\n🎯 Test 1: Enhanced Entry Timing");
            await TestEnhancedEntryTiming(strategy, logger);

            // Test 2: Portfolio Heat Management
            logger.Information("\n🔥 Test 2: Portfolio Heat Management");
            await TestPortfolioHeatManagement(strategy, logger);

            // Test 3: Smart Exit Strategy
            logger.Information("\n🚪 Test 3: Smart Exit Strategy");
            await TestSmartExitStrategy(strategy, marketRegimeAnalyzer, logger);

            // Test 4: Iron Condor Strategy
            logger.Information("\n🦅 Test 4: Iron Condor Strategy");
            await TestIronCondorStrategy(optionsScanner, logger);

            // Test 5: Broken Wing Butterfly Strategy
            logger.Information("\n🦋 Test 5: Broken Wing Butterfly Strategy");
            await TestBrokenWingButterflyStrategy(optionsScanner, marketRegimeAnalyzer, logger);

            // Test 6: Integration Test - Full Signal Generation
            logger.Information("\n🔄 Test 6: Full Signal Generation with New Strategies");
            await TestFullSignalGeneration(strategy, logger);

            logger.Information("\n✅ All Priority 1 & 2 Enhancement Tests Completed Successfully!");
            logger.Information("📊 Summary:");
            logger.Information("  ✅ Enhanced Entry Timing - Dynamic market condition-based entry");
            logger.Information("  ✅ Portfolio Heat Management - Risk aggregation and position sizing");
            logger.Information("  ✅ Smart Exit Strategy - Time and volatility-based exits");
            logger.Information("  ✅ Iron Condor Strategy - High win rate range-bound strategy");
            logger.Information("  ✅ Broken Wing Butterfly - Directional bias strategy");
            logger.Information("  ✅ Full Integration - All strategies working together");

        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error during Priority 1 & 2 enhancements test");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static async Task TestEnhancedEntryTiming(IZeroDteStrategy strategy, ILogger logger)
    {
        try
        {
            logger.Information("Testing enhanced entry timing logic...");
            
            // Test optimal entry time check
            var isOptimalTime = await strategy.IsOptimalEntryTimeAsync();
            logger.Information($"Current time optimal for entry: {isOptimalTime}");
            
            // Test should trade logic (includes enhanced timing)
            var shouldTrade = await strategy.ShouldTrade();
            logger.Information($"Should trade (with enhanced timing): {shouldTrade}");
            
            logger.Information("✅ Enhanced entry timing test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Enhanced entry timing test failed");
        }
    }

    private static async Task TestPortfolioHeatManagement(IZeroDteStrategy strategy, ILogger logger)
    {
        try
        {
            logger.Information("Testing portfolio heat management...");
            
            // Calculate current portfolio heat
            var portfolioHeat = await strategy.CalculatePortfolioHeatAsync();
            logger.Information($"Current portfolio heat: {portfolioHeat:P2}");
            
            // Test heat limits
            var maxHeat = 0.05m; // 5% from configuration
            var heatStatus = portfolioHeat >= maxHeat ? "EXCEEDED" : "WITHIN LIMITS";
            logger.Information($"Heat status: {heatStatus} (Max: {maxHeat:P2})");
            
            logger.Information("✅ Portfolio heat management test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Portfolio heat management test failed");
        }
    }

    private static async Task TestSmartExitStrategy(IZeroDteStrategy strategy, IMarketRegimeAnalyzer marketRegimeAnalyzer, ILogger logger)
    {
        try
        {
            logger.Information("Testing smart exit strategy...");
            
            // Create a mock position for testing
            var mockPosition = new Position
            {
                Id = "TEST_POSITION",
                Strategy = "PutCreditSpread",
                UnderlyingSymbol = "SPY",
                OpenCredit = 100m,
                UnrealizedPnL = 25m, // 25% profit
                ExpirationDate = DateTime.UtcNow.AddHours(2), // 2 hours to expiry
                Status = PositionStatus.Open
            };
            
            // Test exit decision
            var exitDecision = await strategy.ShouldExitPositionAsync(mockPosition);
            logger.Information($"Exit decision: {exitDecision.ShouldExit}");
            logger.Information($"Exit reason: {exitDecision.Reason}");
            
            // Test with different scenarios
            mockPosition.UnrealizedPnL = -150m; // Large loss
            var exitDecisionLoss = await strategy.ShouldExitPositionAsync(mockPosition);
            logger.Information($"Large loss exit decision: {exitDecisionLoss.ShouldExit} - {exitDecisionLoss.Reason}");
            
            logger.Information("✅ Smart exit strategy test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Smart exit strategy test failed");
        }
    }

    private static async Task TestIronCondorStrategy(IOptionsScanner optionsScanner, ILogger logger)
    {
        try
        {
            logger.Information("Testing Iron Condor strategy...");
            
            // Create mock option chain for testing
            var mockChain = CreateMockOptionChain("SPY", 580m);
            
            // Test Iron Condor signal generation
            var condorSignals = await optionsScanner.FindIronCondorOpportunitiesAsync(mockChain);
            
            logger.Information($"Iron Condor signals generated: {condorSignals.Count}");
            foreach (var signal in condorSignals)
            {
                logger.Information($"  Signal: {signal.Reason}");
                logger.Information($"  Expected Profit: {signal.ExpectedProfit:C2}");
                logger.Information($"  Max Loss: {signal.MaxLoss:C2}");
                logger.Information($"  Confidence: {signal.Confidence:P1}");
                logger.Information($"  Legs: {signal.Legs.Count}");
            }
            
            logger.Information("✅ Iron Condor strategy test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Iron Condor strategy test failed");
        }
    }

    private static async Task TestBrokenWingButterflyStrategy(IOptionsScanner optionsScanner, IMarketRegimeAnalyzer marketRegimeAnalyzer, ILogger logger)
    {
        try
        {
            logger.Information("Testing Broken Wing Butterfly strategy...");
            
            // Get current market regime
            var regime = await marketRegimeAnalyzer.GetCurrentRegimeAsync();
            logger.Information($"Current market trend: {regime.Trend}");
            
            // Create mock option chain for testing
            var mockChain = CreateMockOptionChain("SPY", 580m);
            
            // Test Broken Wing Butterfly signal generation
            var bwbSignals = await optionsScanner.FindBrokenWingButterflyOpportunitiesAsync(mockChain);
            
            logger.Information($"Broken Wing Butterfly signals generated: {bwbSignals.Count}");
            foreach (var signal in bwbSignals)
            {
                logger.Information($"  Signal: {signal.Reason}");
                logger.Information($"  Expected Profit: {signal.ExpectedProfit:C2}");
                logger.Information($"  Max Loss: {signal.MaxLoss:C2}");
                logger.Information($"  Confidence: {signal.Confidence:P1}");
                logger.Information($"  Legs: {signal.Legs.Count}");
            }
            
            logger.Information("✅ Broken Wing Butterfly strategy test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Broken Wing Butterfly strategy test failed");
        }
    }

    private static async Task TestFullSignalGeneration(IZeroDteStrategy strategy, ILogger logger)
    {
        try
        {
            logger.Information("Testing full signal generation with all strategies...");
            
            // Generate signals using the enhanced strategy
            var signals = await strategy.GenerateSignalsAsync();
            
            logger.Information($"Total signals generated: {signals.Count}");
            
            // Group by strategy type
            var signalsByStrategy = signals.GroupBy(s => s.Strategy);
            foreach (var group in signalsByStrategy)
            {
                logger.Information($"  {group.Key}: {group.Count()} signals");
                foreach (var signal in group.Take(2)) // Show top 2 per strategy
                {
                    logger.Information($"    - {signal.Reason}");
                    logger.Information($"      Confidence: {signal.Confidence:P1}, R/R: {signal.RiskRewardRatio:F2}");
                }
            }
            
            logger.Information("✅ Full signal generation test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Full signal generation test failed");
        }
    }

    private static OptionChain CreateMockOptionChain(string symbol, decimal price)
    {
        var chain = new OptionChain
        {
            UnderlyingSymbol = symbol,
            UnderlyingPrice = price,
            ExpirationDate = DateTime.Today,
            LastUpdated = DateTime.UtcNow
        };

        // Create mock options around the current price
        for (int i = -50; i <= 50; i += 5)
        {
            var strike = price + i;
            
            // Mock call option
            var callPrice = Math.Max(0.01m, price - strike + 2m);
            chain.Calls.Add(new OptionContract
            {
                Symbol = $"{symbol}_{DateTime.Today:yyMMdd}C{strike:F0}",
                UnderlyingSymbol = symbol,
                OptionType = OptionType.Call,
                StrikePrice = strike,
                ExpirationDate = DateTime.Today,
                Bid = callPrice - 0.05m,
                Ask = callPrice + 0.05m,
                Delta = Math.Max(0.01m, Math.Min(0.99m, 0.5m + (price - strike) / 100m)),
                Volume = 100,
                OpenInterest = 200,
                ImpliedVolatility = 0.25m
            });
            
            // Mock put option
            var putPrice = Math.Max(0.01m, strike - price + 2m);
            chain.Puts.Add(new OptionContract
            {
                Symbol = $"{symbol}_{DateTime.Today:yyMMdd}P{strike:F0}",
                UnderlyingSymbol = symbol,
                OptionType = OptionType.Put,
                StrikePrice = strike,
                ExpirationDate = DateTime.Today,
                Bid = putPrice - 0.05m,
                Ask = putPrice + 0.05m,
                Delta = -Math.Max(0.01m, Math.Min(0.99m, 0.5m + (strike - price) / 100m)),
                Volume = 100,
                OpenInterest = 200,
                ImpliedVolatility = 0.25m
            });
        }

        return chain;
    }
}
