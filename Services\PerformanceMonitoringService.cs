using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace ZeroDateStrat.Services;

public interface IPerformanceMonitoringService
{
    void StartOperation(string operationName);
    void EndOperation(string operationName);
    PerformanceMetrics GetMetrics();
    void LogPerformanceReport();
}

public class PerformanceMetrics
{
    public Dictionary<string, OperationMetrics> Operations { get; set; } = new();
    public SystemMetrics System { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class OperationMetrics
{
    public string Name { get; set; } = string.Empty;
    public long TotalCalls { get; set; }
    public double AverageExecutionTimeMs { get; set; }
    public double MinExecutionTimeMs { get; set; } = double.MaxValue;
    public double MaxExecutionTimeMs { get; set; }
    public double TotalExecutionTimeMs { get; set; }
    public DateTime LastExecuted { get; set; }
}

public class SystemMetrics
{
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageMB { get; set; }
    public int ThreadCount { get; set; }
    public double UptimeHours { get; set; }
    public long GCGen0Collections { get; set; }
    public long GCGen1Collections { get; set; }
    public long GCGen2Collections { get; set; }
    public long GCMemoryMB { get; set; }
}

public class PerformanceMonitoringService : IPerformanceMonitoringService
{
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly ConcurrentDictionary<string, Stopwatch> _activeOperations;
    private readonly ConcurrentDictionary<string, OperationMetrics> _operationMetrics;
    private readonly Process _currentProcess;
    private readonly DateTime _startTime;

    public PerformanceMonitoringService(ILogger<PerformanceMonitoringService> logger)
    {
        _logger = logger;
        _activeOperations = new ConcurrentDictionary<string, Stopwatch>();
        _operationMetrics = new ConcurrentDictionary<string, OperationMetrics>();
        _currentProcess = Process.GetCurrentProcess();
        _startTime = DateTime.UtcNow;
    }

    public void StartOperation(string operationName)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();
            _activeOperations.AddOrUpdate(operationName, stopwatch, (key, existing) =>
            {
                existing.Stop();
                return stopwatch;
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to start performance monitoring for operation: {OperationName}", operationName);
        }
    }

    public void EndOperation(string operationName)
    {
        try
        {
            if (_activeOperations.TryRemove(operationName, out var stopwatch))
            {
                stopwatch.Stop();
                var executionTimeMs = stopwatch.Elapsed.TotalMilliseconds;

                _operationMetrics.AddOrUpdate(operationName, 
                    new OperationMetrics
                    {
                        Name = operationName,
                        TotalCalls = 1,
                        AverageExecutionTimeMs = executionTimeMs,
                        MinExecutionTimeMs = executionTimeMs,
                        MaxExecutionTimeMs = executionTimeMs,
                        TotalExecutionTimeMs = executionTimeMs,
                        LastExecuted = DateTime.UtcNow
                    },
                    (key, existing) =>
                    {
                        existing.TotalCalls++;
                        existing.TotalExecutionTimeMs += executionTimeMs;
                        existing.AverageExecutionTimeMs = existing.TotalExecutionTimeMs / existing.TotalCalls;
                        existing.MinExecutionTimeMs = Math.Min(existing.MinExecutionTimeMs, executionTimeMs);
                        existing.MaxExecutionTimeMs = Math.Max(existing.MaxExecutionTimeMs, executionTimeMs);
                        existing.LastExecuted = DateTime.UtcNow;
                        return existing;
                    });

                // Log slow operations (> 1 second)
                if (executionTimeMs > 1000)
                {
                    _logger.LogWarning("Slow operation detected: {OperationName} took {ExecutionTimeMs:F1}ms", 
                        operationName, executionTimeMs);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to end performance monitoring for operation: {OperationName}", operationName);
        }
    }

    public PerformanceMetrics GetMetrics()
    {
        try
        {
            var metrics = new PerformanceMetrics
            {
                Operations = new Dictionary<string, OperationMetrics>(_operationMetrics),
                System = GetSystemMetrics(),
                LastUpdated = DateTime.UtcNow
            };

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get performance metrics");
            return new PerformanceMetrics();
        }
    }

    public void LogPerformanceReport()
    {
        try
        {
            var metrics = GetMetrics();
            
            _logger.LogInformation("=== PERFORMANCE REPORT ===");
            _logger.LogInformation("System Metrics:");
            _logger.LogInformation("  CPU Usage: {CpuUsage:F1}%", metrics.System.CpuUsagePercent);
            _logger.LogInformation("  Memory Usage: {MemoryUsage}MB", metrics.System.MemoryUsageMB);
            _logger.LogInformation("  Thread Count: {ThreadCount}", metrics.System.ThreadCount);
            _logger.LogInformation("  Uptime: {Uptime:F1} hours", metrics.System.UptimeHours);
            _logger.LogInformation("  GC Collections: Gen0={Gen0}, Gen1={Gen1}, Gen2={Gen2}", 
                metrics.System.GCGen0Collections, metrics.System.GCGen1Collections, metrics.System.GCGen2Collections);

            _logger.LogInformation("Operation Metrics:");
            foreach (var operation in metrics.Operations.Values.OrderByDescending(o => o.TotalCalls))
            {
                _logger.LogInformation("  {OperationName}: {TotalCalls} calls, Avg: {AvgTime:F1}ms, Min: {MinTime:F1}ms, Max: {MaxTime:F1}ms",
                    operation.Name, operation.TotalCalls, operation.AverageExecutionTimeMs, 
                    operation.MinExecutionTimeMs, operation.MaxExecutionTimeMs);
            }
            _logger.LogInformation("=== END PERFORMANCE REPORT ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log performance report");
        }
    }

    private SystemMetrics GetSystemMetrics()
    {
        try
        {
            _currentProcess.Refresh();
            
            return new SystemMetrics
            {
                CpuUsagePercent = GetCpuUsage(),
                MemoryUsageMB = _currentProcess.WorkingSet64 / 1024 / 1024,
                ThreadCount = _currentProcess.Threads.Count,
                UptimeHours = (DateTime.UtcNow - _startTime).TotalHours,
                GCGen0Collections = GC.CollectionCount(0),
                GCGen1Collections = GC.CollectionCount(1),
                GCGen2Collections = GC.CollectionCount(2),
                GCMemoryMB = GC.GetTotalMemory(false) / 1024 / 1024
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get system metrics");
            return new SystemMetrics();
        }
    }

    private double GetCpuUsage()
    {
        try
        {
            // Simple CPU usage calculation (Windows-specific)
            using var counter = new PerformanceCounter("Process", "% Processor Time", _currentProcess.ProcessName);
            counter.NextValue(); // First call returns 0
            Thread.Sleep(100);   // Wait a bit
            return counter.NextValue() / Environment.ProcessorCount;
        }
        catch
        {
            return 0; // Return 0 if we can't measure CPU usage
        }
    }
}

// Extension methods for easy performance monitoring
public static class PerformanceMonitoringExtensions
{
    public static async Task<T> MonitorAsync<T>(this IPerformanceMonitoringService monitor, 
        string operationName, Func<Task<T>> operation)
    {
        monitor.StartOperation(operationName);
        try
        {
            return await operation();
        }
        finally
        {
            monitor.EndOperation(operationName);
        }
    }

    public static T Monitor<T>(this IPerformanceMonitoringService monitor, 
        string operationName, Func<T> operation)
    {
        monitor.StartOperation(operationName);
        try
        {
            return operation();
        }
        finally
        {
            monitor.EndOperation(operationName);
        }
    }

    public static async Task MonitorAsync(this IPerformanceMonitoringService monitor, 
        string operationName, Func<Task> operation)
    {
        monitor.StartOperation(operationName);
        try
        {
            await operation();
        }
        finally
        {
            monitor.EndOperation(operationName);
        }
    }
}
